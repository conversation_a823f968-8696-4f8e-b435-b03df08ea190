import { useCallback, useEffect, useRef, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useAppDispatch, useAppSelector } from "@/common/vendors/redux/store/hook";
import {
  updateSelectedImageCategory,
  setSelectedHierarchicalNodes,
  setSelectedNodeValues,
  updateSelectedGeologySuite,
  updateSelectedGeologyFieldId,
  updateSelectedGeophysicsSuiteId,
  updateSelectedGeophysicsAttributes,
  updateCombinedResultColumnVisibility,
  updateTextWidth,
  updateSelectedHoleNames,
  updateDrillholeView,
} from "../redux/imageSlice";
import { updateProjectId, updateProspectId } from "@/modules/auth/redux/userSlice/user.slice";
import {
  stateToUrlParams,
  urlParamsToState,
  updateUrlParams,
  deserializeTreeValues,
  treeValuesToHierarchicalNodes,
  deserializeGeologySuite,
} from "../utils/url-params.util";
import { EnumDrillholeView } from "../model/enum/drillhole.enum";

/**
 * Custom hook for bidirectional URL and Redux state synchronization
 * Handles reading filter state from URL on mount and updating URL when state changes
 */
export function useUrlFilterSync(
  onDepthParamChange?: (depth: number | null) => void,
  currentDepth?: number | null
) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const dispatch = useAppDispatch();

  // Get current Redux state
  const {
    selectedHoleNames,
    drillholeViewMode,
    selectedImageCategory,
    selectedHierarchicalNodes,
    selectedGeologySuite,
    selectedGeologyFieldId,
    geologySuiteFields,
    selectedGeophysicsSuiteId,
    selectedGeophysicsAttributes,
    geophysicsSuiteAttributes,
    showCombinedResultColumn,
    isTextWide,
  } = useAppSelector((state) => state.images);

  // Get global project and prospect state
  const globalProjectId = useAppSelector((state) => state.user?.userInfo?.projectId);
  const globalProspectId = useAppSelector((state) => state.user?.userInfo?.prospectId);

  // Track pending geology field restoration from URL
  const [pendingGeologyFieldId, setPendingGeologyFieldId] = useState<number | undefined>(undefined);

  // Track pending geophysics attributes restoration from URL
  const [pendingGeophysicsAttributes, setPendingGeophysicsAttributes] = useState<string[] | undefined>(undefined);

  // Track if we're initializing from URL to prevent circular updates
  const isInitializing = useRef(true);
  const lastUrlParams = useRef<string>("");

  /**
   * Initialize Redux state from URL parameters on component mount
   */
  const initializeFromUrl = useCallback(() => {
    const urlParams = urlParamsToState(searchParams);

    // Handle project and prospect parameters with priority over global state
    if (urlParams.projectId !== undefined) {
      // URL parameter takes priority - update global state
      if (urlParams.projectId !== globalProjectId) {
        dispatch(updateProjectId(urlParams.projectId));
      }
    } else if (globalProjectId !== undefined) {
      // No URL parameter but global state exists - this will be handled in updateUrlFromState
    }

    if (urlParams.prospectId !== undefined) {
      // URL parameter takes priority - update global state
      if (urlParams.prospectId !== globalProspectId) {
        dispatch(updateProspectId(urlParams.prospectId));
      }
    } else if (globalProspectId !== undefined) {
      // No URL parameter but global state exists - this will be handled in updateUrlFromState
    }

    // Update Redux state based on URL parameters
    if (urlParams.drillholeNames && urlParams.drillholeNames.length > 0) {
      dispatch(updateSelectedHoleNames(urlParams.drillholeNames));
    }

    if (urlParams.viewMode && Object.values(EnumDrillholeView).includes(urlParams.viewMode as EnumDrillholeView)) {
      dispatch(updateDrillholeView(urlParams.viewMode as EnumDrillholeView));
    }

    if (urlParams.imageCategory !== undefined) {
      dispatch(updateSelectedImageCategory(urlParams.imageCategory));
    }

    if (urlParams.imageTypes) {
      const treeValues = deserializeTreeValues(urlParams.imageTypes);
      const hierarchicalNodes = treeValuesToHierarchicalNodes(treeValues);
      dispatch(setSelectedHierarchicalNodes(hierarchicalNodes));

      // Set the tree values for the TreeSelect component
      dispatch(setSelectedNodeValues(treeValues));
    }

    if (urlParams.geologySuite) {
      const geologySuite = deserializeGeologySuite(urlParams.geologySuite);
      dispatch(updateSelectedGeologySuite(geologySuite));
    }

    // Store geology field for later restoration after geology suite fields are loaded
    if (urlParams.geologyField !== undefined) {
      setPendingGeologyFieldId(urlParams.geologyField);
    }

    if (urlParams.geophysicsSuite !== undefined) {
      dispatch(updateSelectedGeophysicsSuiteId(urlParams.geophysicsSuite));
    }

    // Store geophysics attributes for later restoration after suite attributes are loaded
    if (urlParams.geophysicsAttributes && urlParams.geophysicsAttributes.length > 0) {
      setPendingGeophysicsAttributes(urlParams.geophysicsAttributes);
    }

    if (urlParams.showLogs !== undefined) {
      dispatch(updateCombinedResultColumnVisibility(urlParams.showLogs));
    }

    if (urlParams.textWidth !== undefined) {
      dispatch(updateTextWidth(urlParams.textWidth));
    }

    // Handle depth parameter for parent component
    if (onDepthParamChange && urlParams.depth !== undefined) {
      onDepthParamChange(urlParams.depth);
    } else if (onDepthParamChange) {
      onDepthParamChange(null);
    }

    isInitializing.current = false;
  }, [searchParams, dispatch, onDepthParamChange, globalProjectId, globalProspectId]);

  /**
   * Update URL when Redux state changes (after initialization)
   */
  const updateUrlFromState = useCallback(() => {
    if (isInitializing.current) return;

    const currentState = {
      selectedHoleNames,
      drillholeViewMode,
      selectedImageCategory,
      selectedHierarchicalNodes,
      selectedGeologySuite,
      selectedGeologyFieldId,
      selectedGeophysicsSuiteId,
      selectedGeophysicsAttributes,
      showCombinedResultColumn,
      isTextWide,
    };
    const filterParams = stateToUrlParams(currentState, {
      projectId: globalProjectId,
      prospectId: globalProspectId,
    });

    // Add depth parameter if provided
    if (currentDepth !== undefined && currentDepth !== null) {
      filterParams.depth = currentDepth;
    }

    const newParams = updateUrlParams(searchParams, filterParams);
    const newParamsString = newParams.toString();

    // Only update URL if parameters actually changed
    if (newParamsString !== lastUrlParams.current) {
      lastUrlParams.current = newParamsString;
      router.replace(`${window.location.pathname}?${newParamsString}`, { scroll: false });
    }
  }, [
    router,
    searchParams,
    selectedHoleNames,
    drillholeViewMode,
    selectedImageCategory,
    selectedHierarchicalNodes,
    selectedGeologySuite,
    selectedGeologyFieldId,
    selectedGeophysicsSuiteId,
    selectedGeophysicsAttributes,
    showCombinedResultColumn,
    isTextWide,
    currentDepth,
    globalProjectId,
    globalProspectId,
  ]);

  /**
   * Initialize from URL on component mount
   */
  useEffect(() => {
    initializeFromUrl();
  }, []); // Only run once on mount

  /**
   * Update URL when Redux state changes (after initialization)
   */
  useEffect(() => {
    updateUrlFromState();
  }, [updateUrlFromState]);

  /**
   * Handle delayed geology field restoration when geology suite fields are loaded
   */
  useEffect(() => {
    if (pendingGeologyFieldId !== undefined && geologySuiteFields && geologySuiteFields.length > 0) {
      // Check if the pending geology field ID exists in the loaded fields
      const fieldExists = geologySuiteFields.some((field: any) => field.id === pendingGeologyFieldId);
      if (fieldExists) {

        dispatch(updateSelectedGeologyFieldId(pendingGeologyFieldId));
        setPendingGeologyFieldId(undefined); // Clear pending state
      } else {

        setPendingGeologyFieldId(undefined); // Clear invalid pending state
      }
    }
  }, [geologySuiteFields, pendingGeologyFieldId, dispatch]);

  /**
   * Handle delayed geophysics attributes restoration when geophysics suite attributes are loaded
   */
  useEffect(() => {
    if (pendingGeophysicsAttributes !== undefined && geophysicsSuiteAttributes && geophysicsSuiteAttributes.length > 0) {
      // Validate that the pending attributes exist in the loaded suite attributes
      const validAttributes = pendingGeophysicsAttributes.filter(attrName =>
        geophysicsSuiteAttributes.some((attr: any) => attr.name === attrName)
      );

      if (validAttributes.length > 0) {
        dispatch(updateSelectedGeophysicsAttributes(validAttributes));
      }

      setPendingGeophysicsAttributes(undefined); // Clear pending state regardless
    }
  }, [geophysicsSuiteAttributes, pendingGeophysicsAttributes, dispatch]);

  /**
   * Handle external URL changes (browser back/forward, direct navigation)
   */
  useEffect(() => {
    if (!isInitializing.current) {
      const currentParamsString = searchParams.toString();
      if (currentParamsString !== lastUrlParams.current) {
        lastUrlParams.current = currentParamsString;
        isInitializing.current = true; // Temporarily set to prevent circular updates
        initializeFromUrl();
      }
    }
  }, [searchParams.toString(), initializeFromUrl]);

  return {
    /**
     * Manually trigger URL update (useful for external state changes)
     */
    syncToUrl: updateUrlFromState,

    /**
     * Manually trigger state initialization from URL
     */
    syncFromUrl: initializeFromUrl,
  };
}
